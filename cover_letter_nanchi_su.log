This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.28)  3 AUG 2025 17:43
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**cover_letter_nanchi_su
(./cover_letter_nanchi_su.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/letter.cls
Document Class: letter 2024/08/12 v1.3c Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\longindentation=\dimen141
\indentedwidth=\dimen142
\labelcount=\count192
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18


Package inputenc Warning: inputenc package ignored with utf8 based engines.

) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+lmr on input line 116.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count193
\Gm@cntv=\count194
\c@Gm@tempcnt=\count195
\Gm@bindingoffset=\dimen143
\Gm@wd@mp=\dimen144
\Gm@odd@mp=\dimen145
\Gm@even@mp=\dimen146
\Gm@layoutwidth=\dimen147
\Gm@layoutheight=\dimen148
\Gm@layouthoffset=\dimen149
\Gm@layoutvoffset=\dimen150
\Gm@dimlist=\toks20
) (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count196
)
\MT@toks=\toks21
\MT@tempbox=\box52
\MT@count=\count197
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks22
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen151
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count198
 (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/microtype-xetex.def
File: microtype-xetex.def 2025/02/11 v3.2a Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 443.
LaTeX Info: Redefining \lslig on input line 451.
\MT@outer@space=\skip49
)
Package microtype Info: Loading configuration file microtype.cfg.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/local/texlive/2025/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count199
\l__pdf_internal_box=\box53
\g__pdf_backend_annotation_int=\count266
\g__pdf_backend_link_int=\count267
) (./cover_letter_nanchi_su.aux)
\openout1 = `cover_letter_nanchi_su.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: letterpaper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(57.81621pt, 664.88379pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=664.88379pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-71.45378pt
* \headheight=12.0pt
* \headsep=45.0pt
* \topskip=11.0pt
* \footskip=25.0pt
* \marginparwidth=90.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 2.0pt minus 4.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Info: Redefining \microtypecontext on input line 34.
Package microtype Info: Applying patch `item' on input line 34.
Package microtype Info: Applying patch `toc' on input line 34.
Package microtype Info: Applying patch `eqnum' on input line 34.
Package microtype Info: Applying patch `footnote' on input line 34.
Package microtype Info: Applying patch `verbatim' on input line 34.
LaTeX Info: Redefining \microtypesetup on input line 34.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.
(/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman (RS)
)
LaTeX Font Info:    Trying to load font information for OT1+lmr on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lm/ot1lmr.fd
File: ot1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Trying to load font information for OML+lmm on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lm/omllmm.fd
File: omllmm.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Trying to load font information for OMS+lmsy on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lm/omslmsy.fd
File: omslmsy.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Trying to load font information for OMX+lmex on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lm/omxlmex.fd
File: omxlmex.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <10.95> on input line 49.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <8> on input line 49.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <6> on input line 49.

Missing character: There is no — ("2014) in font ec-lmr10!
Missing character: There is no — ("2014) in font ec-lmr10!


[1

]

[2] (./cover_letter_nanchi_su.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 3878 strings out of 473832
 66554 string characters out of 5729481
 492039 words of memory out of 5000000
 26938 multiletter control sequences out of 15000+600000
 580114 words of font info for 50 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 57i,6n,65p,1015b,171s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on cover_letter_nanchi_su.pdf (2 pages).
